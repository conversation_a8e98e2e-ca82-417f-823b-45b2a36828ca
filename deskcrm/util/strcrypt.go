package util

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"strings"
	"time"
)

const (
	// 与 PHP 版本保持一致的密钥
	CryptKeyA = "d711e5723db5b03e" // 密匙a会参与加解密
	CryptKeyB = "8b912ff2723de14a" // 密匙b会用来做数据完整性验证
)

// EncodeStr 字符串加密，与 PHP 版本 Hk_Util_StrCrypt::encodeStr 保持一致
// 参数:
//   - str: 要加密的字符串
//   - timeLength: 动态密匙长度，默认为2
func EncodeStr(str string, timeLength ...int) string {
	if str == "" {
		return str
	}

	// 设置默认时间密钥长度
	tLen := 2
	if len(timeLength) > 0 && timeLength[0] > 0 {
		tLen = timeLength[0]
	}

	// 动态密匙长度，相同的明文会生成不同密文就是依靠动态密匙
	// 密匙c用于变化生成的密文
	timeKey := getTimeKey(tLen)

	// 参与运算的密匙
	cryptKey := CryptKeyA + md5Hash(CryptKeyA+timeKey)
	keyLength := len(cryptKey)

	// 明文，前16位用来保存keyb(密匙b)，解密时会通过这个密匙验证数据完整性
	str = md5Hash(str+CryptKeyB)[:16] + str
	strLen := len(str)
	result := make([]byte, 0, strLen)

	// 初始化密匙簿
	box := make([]int, 256)
	randKey := make([]int, 256)
	for i := 0; i < 256; i++ {
		box[i] = i
		randKey[i] = int(cryptKey[i%keyLength])
	}

	// 核心加解密部分
	a, j := 0, 0
	for i := 0; i < strLen; i++ {
		a = (a + 1) % 256
		j = (j + box[a] + randKey[a]) % 256
		// 交换
		box[a], box[j] = box[j], box[a]
		// 从密匙簿得出密匙进行异或，再转成字符
		result = append(result, byte(str[i])^byte(box[(box[a]+box[j])%256]))
	}

	// 返回时间密钥 + base64编码结果（去掉等号）
	encoded := base64.StdEncoding.EncodeToString(result)
	encoded = strings.ReplaceAll(encoded, "=", "")
	return timeKey + encoded
}

// DecodeStr 字符串解密，与 PHP 版本 Hk_Util_StrCrypt::decodeStr 保持一致
// 参数:
//   - str: 要解密的字符串
//   - timeLength: 动态密匙长度，默认为2
//   - expire: 过期时间（秒），0表示不检查过期
func DecodeStr(str string, timeLength ...int) string {
	if str == "" {
		return str
	}

	// 设置默认时间密钥长度
	tLen := 2
	if len(timeLength) > 0 && timeLength[0] > 0 {
		tLen = timeLength[0]
	}

	if len(str) < tLen {
		return ""
	}

	// 动态密匙长度，相同的明文会生成不同密文就是依靠动态密匙
	// 密匙c用于变化生成的密文
	timeKey := str[:tLen]

	// 参与运算的密匙
	cryptKey := CryptKeyA + md5Hash(CryptKeyA+timeKey)
	keyLength := len(cryptKey)

	// 解码base64
	encryptedData := str[tLen:]
	// 补充可能缺失的等号
	switch len(encryptedData) % 4 {
	case 2:
		encryptedData += "=="
	case 3:
		encryptedData += "="
	}

	decoded, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return ""
	}

	strLen := len(decoded)
	result := make([]byte, 0, strLen)

	// 初始化密匙簿
	box := make([]int, 256)
	randKey := make([]int, 256)
	for i := 0; i < 256; i++ {
		box[i] = i
		randKey[i] = int(cryptKey[i%keyLength])
	}

	// 核心加解密部分
	a, j := 0, 0
	for i := 0; i < strLen; i++ {
		a = (a + 1) % 256
		j = (j + box[a] + randKey[a]) % 256
		// 交换
		box[a], box[j] = box[j], box[a]
		// 从密匙簿得出密匙进行异或，再转成字符
		result = append(result, decoded[i]^byte(box[(box[a]+box[j])%256]))
	}

	resultStr := string(result)

	// 验证数据有效性，请看未加密明文的格式
	if len(resultStr) < 16 {
		return ""
	}

	// 检查数据完整性
	originalData := resultStr[16:]
	expectedHash := md5Hash(originalData + CryptKeyB)[:16]
	if resultStr[:16] != expectedHash {
		return ""
	}

	return originalData
}

// getTimeKey 生成时间密钥，模拟 PHP 的 substr(md5(microtime()), -$timeLength)
func getTimeKey(length int) string {
	// 使用当前时间的纳秒部分模拟 microtime
	now := time.Now()
	microtime := fmt.Sprintf("%.6f %d", float64(now.UnixNano()%1000000)/1000000, now.Unix())
	hash := md5Hash(microtime)
	if len(hash) < length {
		return hash
	}
	return hash[len(hash)-length:]
}

// md5Hash 计算MD5哈希值
func md5Hash(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return fmt.Sprintf("%x", h.Sum(nil))
}
